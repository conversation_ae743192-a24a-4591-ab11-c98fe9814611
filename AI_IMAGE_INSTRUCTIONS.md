# AI-Generated Professional Person Image Instructions

## Image Requirements

### **Prompt for Leonardo.AI or similar AI generator:**
```
Professional business person working on laptop, modern office setting, clean background, high quality, photorealistic, business casual attire, focused expression, natural lighting, 16:9 aspect ratio, optimized for web
```

### **Technical Specifications:**
- **Format**: JPG or WebP (for optimization)
- **Dimensions**: 800x600 pixels (4:3 ratio) or 1200x675 pixels (16:9 ratio)
- **File Size**: Under 200KB (optimized for web)
- **Quality**: High resolution but web-optimized
- **Background**: Clean, professional office or neutral background

### **Content Guidelines:**
- Professional person (any gender/ethnicity)
- Working on a laptop
- Business casual or professional attire
- Focused, productive expression
- Modern, clean environment
- Good lighting (natural preferred)

## Steps to Add the Image:

1. **Generate the Image:**
   - Use Leonardo.AI, Midjourney, or similar AI image generator
   - Use the prompt above
   - Generate multiple options and choose the best one

2. **Optimize the Image:**
   - Resize to recommended dimensions
   - Compress to under 200KB
   - Convert to WebP format if possible (better compression)
   - Test loading speed

3. **Add to Project:**
   - Save the image as: `/public/images/hero-professional-person.jpg`
   - Or if using WebP: `/public/images/hero-professional-person.webp`

4. **Update Component (if using WebP):**
   - Open `components/Hero.tsx`
   - Change line 51 from `.jpg` to `.webp` if needed

## Alternative Free Options:

### **Unsplash/Pexels:**
- Search for "professional person laptop"
- Download high-quality free stock photo
- Ensure commercial use is allowed
- Optimize as described above

### **Generated Alternatives:**
If AI generation doesn't work, you can use these free stock photo sites:
- Unsplash.com
- Pexels.com
- Pixabay.com

Search terms: "professional working laptop", "business person computer", "office worker laptop"

## Current Status:
- ✅ Hero component updated to use real image
- ✅ Payroll component removed
- ✅ Only 3 components remain (attendance, performance, shifts)
- ✅ Internal animations added to statistics
- ✅ Floating animations removed from containers
- ⏳ **PENDING**: Add the actual professional person image

## File Location:
The image should be placed at: `public/images/hero-professional-person.jpg`

Once you add the image, the hero section will be complete with a welcoming real person and animated internal statistics!
