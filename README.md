# KaziSync Frontend.

A modern attendance management system for organizations.

## Deployment Instructions for Render

### Option 1: Using the Render Dashboard

1. Create a new Web Service in the Render dashboard
2. Connect your GitHub repository
3. Configure the following settings:
   - **Name**: kazi-sync-frontend (or your preferred name)
   - **Environment**: Node
   - **Build Command**: `npm run render-build`
   - **Start Command**: `npm run start`
4. Add the following environment variables:
   - `NODE_ENV`: `production`
   - `NEXT_PUBLIC_API_URL`: `https://kazi.remmittance.com`
5. Click "Create Web Service"

### Option 2: Using render.yaml (Blueprint)

1. Push the render.yaml file to your repository
2. In the Render dashboard, click "Blueprint" and select your repository
3. Follow the prompts to deploy the service

## Local Development

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file with the following content:
   ```
   NEXT_PUBLIC_API_URL=https://kazi.remmittance.com
   ```
4. Start the development server:
   ```
   npm run dev
   ```
5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Build for Production

```
npm run build
npm run start
```

## Project Structure

- `app/`: Next.js app router pages
- `components/`: React components
- `contexts/`: React context providers
- `lib/`: Utility functions and API clients
- `styles/`: Global CSS styles

## Troubleshooting Deployment Issues

### Tailwind CSS Issues

If you encounter issues with Tailwind CSS during deployment, make sure:

1. Tailwind CSS and its dependencies are in the `dependencies` section of package.json, not in `devDependencies`
2. The build command includes `--production=false` to ensure all dependencies are installed
3. The .npmrc file contains `production=false` to override the default behavior

### Module Resolution Issues

If you encounter "Module not found" errors:

1. Check that the path aliases in `tsconfig.json` and `jsconfig.json` are correctly configured
2. Ensure that `next.config.js` includes the webpack configuration for path aliases
3. Verify that all imported modules exist in the correct locations
