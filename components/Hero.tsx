import React from 'react';
import Link from 'next/link';

const Hero = () => {
  return (
    <section className="bg-gradient-to-b from-background to-background-dark py-16 md:py-24">
      <div className="container-custom">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="animate-slide-up">
            <h1 className="text-4xl md:text-5xl font-bold text-secondary-dark mb-6">
              KaziSync – <span className="text-primary">Work in Sync</span>
            </h1>
            <p className="text-lg text-secondary mb-8 max-w-lg">
              The modern HRMS built for teams of all sizes. From payroll to performance, KaziS<PERSON> helps you manage people, not paperwork.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/signup" className="btn-primary text-center px-8 py-3">
                Get Started
              </Link>
              {/* <Link href="#demo" className="btn-outline text-center px-8 py-3">
                Book Demo
              </Link> */}
              <Link href="#contact" className="btn-outline text-center px-8 py-3">
                Contact Sales
              </Link>
            </div>
            <div className="mt-8 flex items-center text-sm text-secondary">
              <svg
                className="h-5 w-5 mr-2 text-success"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span>Try the platform free for 14 days</span>
            </div>
          </div>
          <div className="relative animate-fade-in">
            {/* Main Hero Image Container */}
            <div className="relative bg-gradient-to-br from-white to-gray-50 rounded-3xl border border-gray-200 p-8 md:p-12 overflow-hidden">
              {/* Central Person with Laptop */}
              <div className="relative z-20 flex justify-center items-center">
                <div className="relative">
                  <img
                    src="/images/hero-professional-person.svg"
                    alt="Professional using KaziSync on laptop"
                    className="w-80 md:w-96 h-auto object-contain rounded-2xl"
                    style={{ filter: 'drop-shadow(0 10px 30px rgba(0,123,255,0.1))' }}
                  />
                </div>
              </div>

              {/* Dashboard Components */}
              {/* Attendance Analytics - Top Left */}
              <div className="absolute top-8 left-8 z-10 hidden md:block">
                <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-100 w-48 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-center mb-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-gray-700">Attendance Analytics</span>
                  </div>
                  <svg className="w-full h-16" viewBox="0 0 200 60">
                    <path
                      d="M10,50 Q50,20 90,30 T170,25"
                      stroke="#007BFF"
                      strokeWidth="3"
                      fill="none"
                      opacity="0.8"
                      className="animate-pulse"
                      style={{ animationDuration: '2s' }}
                    />
                    <circle cx="170" cy="25" r="4" fill="#007BFF" className="animate-ping" style={{ animationDuration: '2s' }} />
                  </svg>
                  <div className="text-xs text-gray-500 mt-1">95% Present Today</div>
                </div>
              </div>

              {/* Performance Score - Top Right */}
              <div className="absolute top-12 right-8 z-10 hidden lg:block">
                <div className="bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-100 w-44 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-center mb-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-gray-700">Performance Score</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <svg className="w-12 h-12 transform -rotate-90">
                      <circle
                        cx="24"
                        cy="24"
                        r="18"
                        stroke="#E5E7EB"
                        strokeWidth="4"
                        fill="none"
                      />
                      <circle
                        cx="24"
                        cy="24"
                        r="18"
                        stroke="#8B5CF6"
                        strokeWidth="4"
                        fill="none"
                        strokeDasharray="113"
                        strokeDashoffset="25"
                        strokeLinecap="round"
                        className="animate-pulse"
                        style={{
                          animationDuration: '3s',
                          animation: 'progressFill 3s ease-in-out infinite'
                        }}
                      />
                    </svg>
                    <div className="ml-2">
                      <div className="text-lg font-bold text-purple-600 animate-pulse" style={{ animationDuration: '3s' }}>87%</div>
                      <div className="text-xs text-gray-500">Team Score</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shift Schedule - Bottom Left */}
              <div className="absolute bottom-8 left-12 z-10 hidden md:block">
                <div className="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg border border-gray-100 w-36 hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-center mb-2">
                    <div className="w-3 h-3 bg-indigo-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-gray-700">Shifts</span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center text-xs">
                      <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" style={{ animationDelay: '0s', animationDuration: '2s' }}></div>
                      <span className="text-gray-600">Morning: 8</span>
                    </div>
                    <div className="flex items-center text-xs">
                      <div className="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse" style={{ animationDelay: '0.5s', animationDuration: '2s' }}></div>
                      <span className="text-gray-600">Evening: 5</span>
                    </div>
                    <div className="flex items-center text-xs">
                      <div className="w-2 h-2 bg-purple-400 rounded-full mr-2 animate-pulse" style={{ animationDelay: '1s', animationDuration: '2s' }}></div>
                      <span className="text-gray-600">Night: 3</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Background Decorative Elements */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-full z-0"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-primary-light/5 to-transparent rounded-full z-0"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
