<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="200" cy="150" r="140" fill="url(#bgGradient)" opacity="0.1"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007BFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4DA3FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="skinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FDBCB4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F4A6A0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="laptopGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E5E7EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1D5DB;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1F2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Person Body -->
  <ellipse cx="200" cy="240" rx="45" ry="25" fill="#4F46E5" opacity="0.8"/>
  
  <!-- Person Torso -->
  <rect x="175" y="180" width="50" height="70" rx="25" fill="#4F46E5"/>
  
  <!-- Person Arms -->
  <ellipse cx="155" cy="200" rx="12" ry="30" fill="url(#skinGradient)" transform="rotate(-20 155 200)"/>
  <ellipse cx="245" cy="200" rx="12" ry="30" fill="url(#skinGradient)" transform="rotate(20 245 200)"/>
  
  <!-- Person Hands -->
  <circle cx="145" cy="220" r="8" fill="url(#skinGradient)"/>
  <circle cx="255" cy="220" r="8" fill="url(#skinGradient)"/>
  
  <!-- Person Head -->
  <circle cx="200" cy="140" r="25" fill="url(#skinGradient)"/>
  
  <!-- Hair -->
  <path d="M175 125 Q200 110 225 125 Q225 140 200 145 Q175 140 175 125" fill="#2D1B69"/>
  
  <!-- Eyes -->
  <circle cx="192" cy="135" r="2" fill="#1F2937"/>
  <circle cx="208" cy="135" r="2" fill="#1F2937"/>
  
  <!-- Smile -->
  <path d="M190 145 Q200 150 210 145" stroke="#1F2937" stroke-width="1.5" fill="none" stroke-linecap="round"/>
  
  <!-- Laptop Base -->
  <rect x="120" y="210" width="160" height="20" rx="8" fill="url(#laptopGradient)"/>
  
  <!-- Laptop Screen -->
  <rect x="130" y="120" width="140" height="95" rx="8" fill="url(#laptopGradient)"/>
  
  <!-- Laptop Screen Display -->
  <rect x="140" y="130" width="120" height="75" rx="4" fill="url(#screenGradient)"/>
  
  <!-- Screen Content - KaziSync Dashboard -->
  <!-- Header Bar -->
  <rect x="145" y="135" width="110" height="8" rx="2" fill="#007BFF" opacity="0.8"/>
  
  <!-- Navigation -->
  <rect x="145" y="148" width="25" height="3" rx="1" fill="#4DA3FF" opacity="0.6"/>
  <rect x="175" y="148" width="20" height="3" rx="1" fill="#6C757D" opacity="0.4"/>
  <rect x="200" y="148" width="22" height="3" rx="1" fill="#6C757D" opacity="0.4"/>
  
  <!-- Dashboard Cards -->
  <rect x="145" y="158" width="30" height="20" rx="2" fill="#F8F9FA" opacity="0.9"/>
  <rect x="180" y="158" width="30" height="20" rx="2" fill="#F8F9FA" opacity="0.9"/>
  <rect x="215" y="158" width="30" height="20" rx="2" fill="#F8F9FA" opacity="0.9"/>
  
  <!-- Chart Lines -->
  <path d="M148 165 L155 160 L162 168 L169 162 L172 165" stroke="#007BFF" stroke-width="1" fill="none"/>
  <path d="M183 170 L190 165 L197 172 L204 168 L207 170" stroke="#28A745" stroke-width="1" fill="none"/>
  
  <!-- Data Points -->
  <circle cx="220" cy="165" r="1.5" fill="#007BFF"/>
  <circle cx="225" cy="168" r="1.5" fill="#007BFF"/>
  <circle cx="230" cy="162" r="1.5" fill="#007BFF"/>
  <circle cx="235" cy="170" r="1.5" fill="#007BFF"/>
  
  <!-- Bottom Section -->
  <rect x="145" y="185" width="110" height="15" rx="2" fill="#E9ECEF" opacity="0.7"/>
  <rect x="148" y="188" width="20" height="2" rx="1" fill="#007BFF" opacity="0.8"/>
  <rect x="148" y="192" width="35" height="2" rx="1" fill="#6C757D" opacity="0.6"/>
  <rect x="148" y="196" width="25" height="2" rx="1" fill="#6C757D" opacity="0.6"/>
  
  <!-- Keyboard -->
  <rect x="135" y="215" width="130" height="10" rx="2" fill="#D1D5DB"/>
  
  <!-- Keyboard Keys -->
  <rect x="140" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="146" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="152" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="158" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="164" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="170" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="176" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="182" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="188" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="194" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="200" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="206" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="212" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="218" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="224" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="230" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="236" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="242" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="248" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="254" y="217" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  
  <!-- Second row of keys -->
  <rect x="142" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="148" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="154" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="160" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="166" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="172" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="178" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="184" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="190" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="196" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="202" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="208" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="214" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="220" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="226" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="232" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="238" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="244" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="250" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
  <rect x="256" y="221" width="4" height="3" rx="0.5" fill="#9CA3AF"/>
</svg>
